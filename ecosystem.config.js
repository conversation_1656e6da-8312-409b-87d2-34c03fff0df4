module.exports = {
  apps: [
    {
      name: 'telegram-shop',
      script: './src/app.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        LOG_LEVEL: 'info'
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000,
        LOG_LEVEL: 'debug'
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3000,
        LOG_LEVEL: 'info'
      },
      // PM2配置
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'database'],
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 进程配置
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 自动重启配置
      autorestart: true,
      cron_restart: '0 2 * * *', // 每天凌晨2点重启
      
      // 健康检查
      health_check_grace_period: 3000,
      
      // 环境变量
      env_file: '.env'
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/telegram-shop.git',
      path: '/var/www/telegram-shop',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'deploy',
      host: ['staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-username/telegram-shop.git',
      path: '/var/www/telegram-shop-staging',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env staging'
    }
  }
};
