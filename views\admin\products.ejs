<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - <%= siteName %></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .badge-status {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">
                            <i class="fas fa-store me-2"></i>
                            卡密销售系统
                        </h5>
                        <small class="text-white-50">欢迎，<%= admin.username %></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'products' ? 'active' : '' %>" href="/admin/products">
                                <i class="fas fa-box me-2"></i>
                                商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'orders' ? 'active' : '' %>" href="/admin/orders">
                                <i class="fas fa-shopping-cart me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'cards' ? 'active' : '' %>" href="/admin/cards">
                                <i class="fas fa-credit-card me-2"></i>
                                卡密管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'users' ? 'active' : '' %>" href="/admin/users">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'reports' ? 'active' : '' %>" href="/admin/reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'settings' ? 'active' : '' %>" href="/admin/settings">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-box me-2"></i>
                        商品管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                <i class="fas fa-plus me-1"></i>
                                添加商品
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>商品名称</th>
                                        <th>分类</th>
                                        <th>价格</th>
                                        <th>库存</th>
                                        <th>已售</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (products && products.length > 0) { %>
                                        <% products.forEach(product => { %>
                                            <tr>
                                                <td><%= product.id %></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <% if (product.image_url) { %>
                                                            <img src="<%= product.image_url %>" alt="<%= product.name %>" class="product-image me-3">
                                                        <% } else { %>
                                                            <div class="bg-secondary product-image me-3 d-flex align-items-center justify-content-center">
                                                                <i class="fas fa-image text-white"></i>
                                                            </div>
                                                        <% } %>
                                                        <div>
                                                            <h6 class="mb-0"><%= product.name %></h6>
                                                            <% if (product.description) { %>
                                                                <small class="text-muted"><%= product.description.substring(0, 50) %>...</small>
                                                            <% } %>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><%= product.category_name || '未分类' %></td>
                                                <td>
                                                    <strong>¥<%= parseFloat(product.price).toFixed(2) %></strong>
                                                    <% if (product.original_price && product.original_price > product.price) { %>
                                                        <br><small class="text-muted text-decoration-line-through">¥<%= parseFloat(product.original_price).toFixed(2) %></small>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <span class="badge <%= product.available_cards > product.min_stock_alert ? 'bg-success' : 'bg-warning' %>">
                                                        <%= product.available_cards %>
                                                    </span>
                                                </td>
                                                <td><%= product.sold_count %></td>
                                                <td>
                                                    <% if (product.status === 'active') { %>
                                                        <span class="badge bg-success badge-status">上架</span>
                                                    <% } else if (product.status === 'inactive') { %>
                                                        <span class="badge bg-secondary badge-status">下架</span>
                                                    <% } else { %>
                                                        <span class="badge bg-danger badge-status">缺货</span>
                                                    <% } %>
                                                </td>
                                                <td><%= new Date(product.created_at).toLocaleDateString('zh-CN') %></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary" title="编辑" onclick="editProduct(<%= product.id %>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-info" title="查看卡密" onclick="viewProductCards(<%= product.id %>, '<%= product.name %>')">
                                                            <i class="fas fa-credit-card"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger" title="删除" onclick="deleteProduct(<%= product.id %>, '<%= product.name %>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <i class="fas fa-box-open fa-3x mb-3 opacity-25"></i>
                                                <p>暂无商品数据</p>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <% if (pagination && pagination.totalPages > 1) { %>
                            <nav aria-label="商品分页">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item <%= !pagination.hasPrev ? 'disabled' : '' %>">
                                        <a class="page-link" href="?page=<%= pagination.page - 1 %>">上一页</a>
                                    </li>
                                    
                                    <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                                        <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                                            <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                                        </li>
                                    <% } %>
                                    
                                    <li class="page-item <%= !pagination.hasNext ? 'disabled' : '' %>">
                                        <a class="page-link" href="?page=<%= pagination.page + 1 %>">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        <% } %>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加商品模态框 -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        添加商品
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productName" class="form-label">商品名称 *</label>
                                    <input type="text" class="form-control" id="productName" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productCategory" class="form-label">商品分类</label>
                                    <select class="form-select" id="productCategory" name="category_id">
                                        <option value="">请选择分类</option>
                                        <% if (categories && categories.length > 0) { %>
                                            <% categories.forEach(category => { %>
                                                <option value="<%= category.id %>"><%= category.name %></option>
                                            <% }); %>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="productDescription" class="form-label">商品描述</label>
                            <textarea class="form-control" id="productDescription" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productPrice" class="form-label">销售价格 *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="productPrice" name="price" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productOriginalPrice" class="form-label">原价</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="productOriginalPrice" name="original_price" step="0.01">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productMinStock" class="form-label">库存预警</label>
                                    <input type="number" class="form-control" id="productMinStock" name="min_stock_alert" value="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productSortOrder" class="form-label">排序</label>
                                    <input type="number" class="form-control" id="productSortOrder" name="sort_order" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="productImage" class="form-label">商品图片</label>
                            <input type="url" class="form-control" id="productImage" name="image_url" placeholder="图片URL">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitProduct()">
                        <i class="fas fa-save me-1"></i>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑商品模态框 -->
    <div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProductModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        编辑商品
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editProductForm">
                        <input type="hidden" id="editProductId" name="id">

                        <div class="mb-3">
                            <label for="editProductName" class="form-label">商品名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editProductName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="editProductCategory" class="form-label">商品分类</label>
                            <select class="form-select" id="editProductCategory" name="category_id">
                                <option value="">请选择分类</option>
                                <% if (categories && categories.length > 0) { %>
                                    <% categories.forEach(category => { %>
                                        <option value="<%= category.id %>"><%= category.name %></option>
                                    <% }); %>
                                <% } %>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="editProductDescription" class="form-label">商品描述</label>
                            <textarea class="form-control" id="editProductDescription" name="description" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editProductPrice" class="form-label">售价 <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="editProductPrice" name="price" step="0.01" min="0" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editProductOriginalPrice" class="form-label">原价</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="editProductOriginalPrice" name="original_price" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="editProductMinStock" class="form-label">库存预警</label>
                            <input type="number" class="form-control" id="editProductMinStock" name="min_stock_alert" min="0" value="10">
                            <div class="form-text">当库存低于此数量时会显示预警</div>
                        </div>

                        <div class="mb-3">
                            <label for="editProductImage" class="form-label">商品图片</label>
                            <input type="url" class="form-control" id="editProductImage" name="image_url" placeholder="图片URL">
                        </div>

                        <div class="mb-3">
                            <label for="editProductStatus" class="form-label">商品状态</label>
                            <select class="form-select" id="editProductStatus" name="status">
                                <option value="active">上架</option>
                                <option value="inactive">下架</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateProduct()">
                        <i class="fas fa-save me-1"></i>
                        保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看商品卡密模态框 -->
    <div class="modal fade" id="viewCardsModal" tabindex="-1" aria-labelledby="viewCardsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewCardsModalLabel">
                        <i class="fas fa-credit-card me-2"></i>
                        商品卡密列表
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="cardsLoadingSpinner" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载卡密数据...</p>
                    </div>

                    <div id="cardsContent" style="display: none;">
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title mb-1">可用卡密</h6>
                                            <h4 class="text-success mb-0" id="availableCardsCount">0</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title mb-1">已售卡密</h6>
                                            <h4 class="text-primary mb-0" id="soldCardsCount">0</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>卡号</th>
                                        <th>密码</th>
                                        <th>状态</th>
                                        <th>批次</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody id="cardsTableBody">
                                    <!-- 卡密数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>

                        <div id="cardsEmptyState" class="text-center py-4" style="display: none;">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <p class="text-muted">该商品暂无卡密</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a href="#" id="manageCardsLink" class="btn btn-primary">
                        <i class="fas fa-cog me-1"></i>
                        管理卡密
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 统一的提示函数
        function showToast(message, type = 'success') {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(toast);

            // 3秒后自动消失
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }

        // 显示加载状态
        function showLoading(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
            button.disabled = true;
            return originalText;
        }

        // 隐藏加载状态
        function hideLoading(button, originalText) {
            button.innerHTML = originalText;
            button.disabled = false;
        }

        function submitProduct() {
            const form = document.getElementById('addProductForm');
            const submitBtn = form.querySelector('button[onclick="submitProduct()"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            fetch('/api/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                hideLoading(submitBtn, originalText);
                if (result.success) {
                    showToast('商品添加成功！', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('添加失败: ' + result.message, 'danger');
                }
            })
            .catch(error => {
                hideLoading(submitBtn, originalText);
                showToast('添加失败: ' + error.message, 'danger');
            });
        }

        // 编辑商品
        function editProduct(productId) {
            fetch(`/api/products/${productId}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const product = result.data;

                        // 填充表单数据
                        document.getElementById('editProductId').value = product.id;
                        document.getElementById('editProductName').value = product.name || '';
                        document.getElementById('editProductCategory').value = product.category_id || '';
                        document.getElementById('editProductDescription').value = product.description || '';
                        document.getElementById('editProductPrice').value = product.price || '';
                        document.getElementById('editProductOriginalPrice').value = product.original_price || '';
                        document.getElementById('editProductMinStock').value = product.min_stock_alert || 10;
                        document.getElementById('editProductImage').value = product.image_url || '';
                        document.getElementById('editProductStatus').value = product.status || 'active';

                        // 显示模态框
                        const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
                        modal.show();
                    } else {
                        alert('获取商品信息失败: ' + result.message);
                    }
                })
                .catch(error => {
                    alert('获取商品信息失败: ' + error.message);
                });
        }

        // 更新商品
        function updateProduct() {
            const form = document.getElementById('editProductForm');
            const submitBtn = form.querySelector('button[onclick="updateProduct()"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            const productId = data.id;

            // 移除id字段，避免在更新时传递
            delete data.id;

            fetch(`/api/products/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                hideLoading(submitBtn, originalText);
                if (result.success) {
                    showToast('商品更新成功！', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('更新失败: ' + result.message, 'danger');
                }
            })
            .catch(error => {
                hideLoading(submitBtn, originalText);
                showToast('更新失败: ' + error.message, 'danger');
            });
        }

        // 删除商品
        function deleteProduct(productId, productName) {
            if (confirm(`确定要删除商品 "${productName}" 吗？\n\n注意：删除商品将同时删除其关联的所有卡密！`)) {
                // 显示删除进度
                showToast('正在删除商品...', 'info');

                fetch(`/api/products/${productId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showToast('商品删除成功！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showToast('删除失败: ' + result.message, 'danger');
                    }
                })
                .catch(error => {
                    showToast('删除失败: ' + error.message, 'danger');
                });
            }
        }

        // 查看商品卡密
        function viewProductCards(productId, productName) {
            // 更新模态框标题
            document.getElementById('viewCardsModalLabel').innerHTML =
                `<i class="fas fa-credit-card me-2"></i>${productName} - 卡密列表`;

            // 设置管理卡密链接
            document.getElementById('manageCardsLink').href = `/admin/cards?product_id=${productId}`;

            // 显示加载状态
            document.getElementById('cardsLoadingSpinner').style.display = 'block';
            document.getElementById('cardsContent').style.display = 'none';

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('viewCardsModal'));
            modal.show();

            // 获取卡密数据
            fetch(`/api/products/${productId}/cards`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const cards = result.data.cards || [];

                        // 统计卡密状态
                        const availableCards = cards.filter(card => card.status === 'available').length;
                        const soldCards = cards.filter(card => card.status === 'sold').length;

                        // 更新统计数据
                        document.getElementById('availableCardsCount').textContent = availableCards;
                        document.getElementById('soldCardsCount').textContent = soldCards;

                        // 填充卡密表格
                        const tableBody = document.getElementById('cardsTableBody');
                        tableBody.innerHTML = '';

                        if (cards.length > 0) {
                            cards.forEach(card => {
                                const row = document.createElement('tr');

                                // 状态样式
                                let statusBadge = '';
                                switch(card.status) {
                                    case 'available':
                                        statusBadge = '<span class="badge bg-success">可用</span>';
                                        break;
                                    case 'sold':
                                        statusBadge = '<span class="badge bg-primary">已售</span>';
                                        break;
                                    case 'expired':
                                        statusBadge = '<span class="badge bg-danger">过期</span>';
                                        break;
                                    default:
                                        statusBadge = `<span class="badge bg-secondary">${card.status}</span>`;
                                }

                                row.innerHTML = `
                                    <td><code>${card.card_number}</code></td>
                                    <td><code>${card.card_password}</code></td>
                                    <td>${statusBadge}</td>
                                    <td><small class="text-muted">${card.batch_id || '-'}</small></td>
                                    <td><small class="text-muted">${new Date(card.created_at).toLocaleString('zh-CN')}</small></td>
                                `;

                                tableBody.appendChild(row);
                            });

                            document.getElementById('cardsEmptyState').style.display = 'none';
                        } else {
                            document.getElementById('cardsEmptyState').style.display = 'block';
                        }

                        // 隐藏加载状态，显示内容
                        document.getElementById('cardsLoadingSpinner').style.display = 'none';
                        document.getElementById('cardsContent').style.display = 'block';

                    } else {
                        alert('获取卡密数据失败: ' + result.message);
                        modal.hide();
                    }
                })
                .catch(error => {
                    alert('获取卡密数据失败: ' + error.message);
                    modal.hide();
                });
        }
    </script>
</body>
</html>
